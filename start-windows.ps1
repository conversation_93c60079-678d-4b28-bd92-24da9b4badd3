#!/usr/bin/env pwsh
# Windows Docker Compose 一键启动脚本
# 使用方法: .\start-windows.ps1 [选项]

param(
    [switch]$Clean,           # 清理现有容器和卷
    [switch]$Build,           # 重新构建镜像
    [switch]$Logs,            # 显示实时日志
    [switch]$Monitor,         # 启动监控服务
    [switch]$Help,            # 显示帮助信息
    [string]$Service = "",    # 启动特定服务
    [string]$Profile = "all"  # 启动配置文件 (basic|full|all)
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Info($message) {
    Write-ColorOutput Cyan "ℹ️  $message"
}

function Write-Success($message) {
    Write-ColorOutput Green "✅ $message"
}

function Write-Warning($message) {
    Write-ColorOutput Yellow "⚠️  $message"
}

function Write-Error($message) {
    Write-ColorOutput Red "❌ $message"
}

function Write-Header($message) {
    Write-Host ""
    Write-ColorOutput Magenta "🚀 $message"
    Write-Host "=" * 60
}

# 显示帮助信息
function Show-Help {
    Write-Host @"
Windows Docker Compose 部署脚本

用法: .\start-windows.ps1 [选项]

选项:
  -Clean          清理现有容器、网络和卷
  -Build          重新构建所有Docker镜像
  -Logs           启动后显示实时日志
  -Monitor        同时启动监控服务 (Prometheus + Grafana)
  -Help           显示此帮助信息
  -Service <名称> 仅启动指定服务
  -Profile <类型> 启动配置 (basic|full|all)

配置文件说明:
  basic  - 仅启动基础设施 (数据库、缓存等)
  full   - 启动所有业务服务 (不包含监控)
  all    - 启动所有服务 (包含监控)

示例:
  .\start-windows.ps1                    # 启动所有服务
  .\start-windows.ps1 -Clean -Build      # 清理并重新构建启动
  .\start-windows.ps1 -Service mysql     # 仅启动MySQL服务
  .\start-windows.ps1 -Profile basic     # 仅启动基础设施
  .\start-windows.ps1 -Logs              # 启动并显示日志
"@
}

# 检查Docker是否运行
function Test-DockerRunning {
    try {
        docker info | Out-Null
        return $true
    } catch {
        return $false
    }
}

# 检查Docker Compose是否可用
function Test-DockerCompose {
    try {
        docker-compose --version | Out-Null
        return $true
    } catch {
        try {
            docker compose version | Out-Null
            return $true
        } catch {
            return $false
        }
    }
}

# 获取Docker Compose命令
function Get-DockerComposeCommand {
    try {
        docker-compose --version | Out-Null
        return "docker-compose"
    } catch {
        return "docker compose"
    }
}

# 检查环境文件
function Test-EnvironmentFile {
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.windows") {
            Write-Info "复制 .env.windows 为 .env"
            Copy-Item ".env.windows" ".env"
        } else {
            Write-Error "环境文件 .env 不存在，请先创建环境配置文件"
            Write-Info "可以复制 .env.windows 作为模板："
            Write-Info "Copy-Item .env.windows .env"
            exit 1
        }
    }
}

# 创建必要的目录
function New-RequiredDirectories {
    $directories = @(
        "data",
        "data/mysql",
        "data/redis", 
        "data/minio",
        "data/chroma",
        "data/uploads",
        "data/uploads/assets",
        "data/uploads/knowledge",
        "data/outputs",
        "data/outputs/renders",
        "data/models",
        "data/prometheus",
        "data/grafana",
        "logs",
        "temp",
        "backups"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Info "创建目录: $dir"
        }
    }
}

# 检查系统资源
function Test-SystemResources {
    $memory = Get-CimInstance -ClassName Win32_ComputerSystem | Select-Object -ExpandProperty TotalPhysicalMemory
    $memoryGB = [math]::Round($memory / 1GB, 2)
    
    if ($memoryGB -lt 8) {
        Write-Warning "系统内存不足 8GB (当前: ${memoryGB}GB)，可能影响性能"
    } else {
        Write-Success "系统内存检查通过 (${memoryGB}GB)"
    }
    
    $freeSpace = Get-CimInstance -ClassName Win32_LogicalDisk -Filter "DeviceID='C:'" | Select-Object -ExpandProperty FreeSpace
    $freeSpaceGB = [math]::Round($freeSpace / 1GB, 2)
    
    if ($freeSpaceGB -lt 20) {
        Write-Warning "磁盘空间不足 20GB (当前: ${freeSpaceGB}GB)，可能影响运行"
    } else {
        Write-Success "磁盘空间检查通过 (${freeSpaceGB}GB 可用)"
    }
}

# 启动基础设施服务
function Start-InfrastructureServices($composeCmd) {
    Write-Header "启动基础设施服务"
    
    $infraServices = @("mysql", "redis", "minio", "chroma")
    
    foreach ($service in $infraServices) {
        Write-Info "启动服务: $service"
        & $composeCmd -f docker-compose.windows.yml up -d $service
        if ($LASTEXITCODE -ne 0) {
            Write-Error "启动 $service 失败"
            exit 1
        }
    }
    
    Write-Info "等待基础设施服务启动完成..."
    Start-Sleep -Seconds 60
    
    # 检查服务健康状态
    Write-Info "检查服务健康状态..."
    & $composeCmd -f docker-compose.windows.yml ps
}

# 启动核心服务
function Start-CoreServices($composeCmd) {
    Write-Header "启动核心微服务"
    
    # 启动服务注册中心
    Write-Info "启动服务注册中心..."
    & $composeCmd -f docker-compose.windows.yml up -d service-registry
    Start-Sleep -Seconds 30
    
    # 启动API网关和核心服务
    $coreServices = @("api-gateway", "user-service", "project-service", "asset-service")
    
    foreach ($service in $coreServices) {
        Write-Info "启动服务: $service"
        & $composeCmd -f docker-compose.windows.yml up -d $service
        Start-Sleep -Seconds 15
    }
}

# 启动业务服务
function Start-BusinessServices($composeCmd) {
    Write-Header "启动业务微服务"
    
    $businessServices = @(
        "render-service",
        "collaboration-service-1", 
        "collaboration-service-2",
        "ai-model-service",
        "knowledge-service",
        "rag-engine"
    )
    
    foreach ($service in $businessServices) {
        Write-Info "启动服务: $service"
        & $composeCmd -f docker-compose.windows.yml up -d $service
        Start-Sleep -Seconds 20
    }
    
    # 启动负载均衡器
    Write-Info "启动协作服务负载均衡器..."
    & $composeCmd -f docker-compose.windows.yml up -d collaboration-load-balancer
    Start-Sleep -Seconds 10
}

# 启动前端服务
function Start-FrontendServices($composeCmd) {
    Write-Header "启动前端编辑器"
    
    Write-Info "启动编辑器前端..."
    & $composeCmd -f docker-compose.windows.yml up -d editor
    Start-Sleep -Seconds 15
}

# 启动监控服务
function Start-MonitoringServices($composeCmd) {
    Write-Header "启动监控服务"
    
    Write-Info "启动 Prometheus..."
    & $composeCmd -f docker-compose.windows.yml up -d prometheus
    Start-Sleep -Seconds 15
    
    Write-Info "启动 Grafana..."
    & $composeCmd -f docker-compose.windows.yml up -d grafana
    Start-Sleep -Seconds 15
}

# 显示服务状态
function Show-ServiceStatus($composeCmd) {
    Write-Header "服务状态"
    & $composeCmd -f docker-compose.windows.yml ps
    
    Write-Header "访问地址"
    Write-Info "🌐 前端编辑器: http://localhost"
    Write-Info "🔌 API网关: http://localhost:3000"
    Write-Info "📊 MinIO控制台: http://localhost:9001 (用户名: dlengine)"
    Write-Info "🔍 Chroma API: http://localhost:8000"
    
    if ($Monitor) {
        Write-Info "📈 Prometheus: http://localhost:9090"
        Write-Info "📊 Grafana: http://localhost:3000 (用户名: admin)"
    }
}

# 主函数
function Main {
    if ($Help) {
        Show-Help
        return
    }
    
    Write-Header "DL Engine Windows Docker Compose 部署"
    
    # 检查Docker状态
    if (-not (Test-DockerRunning)) {
        Write-Error "Docker Desktop 未运行，请先启动 Docker Desktop"
        exit 1
    }
    Write-Success "Docker Desktop 运行正常"
    
    # 检查Docker Compose
    if (-not (Test-DockerCompose)) {
        Write-Error "Docker Compose 不可用，请检查 Docker Desktop 安装"
        exit 1
    }
    
    $composeCmd = Get-DockerComposeCommand
    Write-Success "Docker Compose 可用 ($composeCmd)"
    
    # 检查环境文件
    Test-EnvironmentFile
    Write-Success "环境配置文件检查通过"
    
    # 创建必要目录
    New-RequiredDirectories
    Write-Success "目录结构创建完成"
    
    # 检查系统资源
    Test-SystemResources
    
    # 清理选项
    if ($Clean) {
        Write-Warning "清理现有容器和卷..."
        & $composeCmd -f docker-compose.windows.yml down -v --remove-orphans
        docker system prune -f
        Write-Success "清理完成"
    }
    
    # 构建选项
    if ($Build) {
        Write-Info "构建Docker镜像..."
        & $composeCmd -f docker-compose.windows.yml build --no-cache
        Write-Success "镜像构建完成"
    }
    
    # 启动特定服务
    if ($Service) {
        Write-Info "启动服务: $Service"
        & $composeCmd -f docker-compose.windows.yml up -d $Service
        if ($Logs) {
            & $composeCmd -f docker-compose.windows.yml logs -f $Service
        }
        return
    }
    
    # 根据配置文件启动服务
    switch ($Profile) {
        "basic" {
            Start-InfrastructureServices $composeCmd
        }
        "full" {
            Start-InfrastructureServices $composeCmd
            Start-CoreServices $composeCmd
            Start-BusinessServices $composeCmd
            Start-FrontendServices $composeCmd
        }
        "all" {
            Start-InfrastructureServices $composeCmd
            Start-CoreServices $composeCmd
            Start-BusinessServices $composeCmd
            Start-FrontendServices $composeCmd
            if ($Monitor) {
                Start-MonitoringServices $composeCmd
            }
        }
        default {
            Start-InfrastructureServices $composeCmd
            Start-CoreServices $composeCmd
            Start-BusinessServices $composeCmd
            Start-FrontendServices $composeCmd
        }
    }
    
    Write-Success "🎉 所有服务启动完成！"
    
    # 显示服务状态
    Show-ServiceStatus $composeCmd
    
    # 显示日志选项
    if ($Logs) {
        Write-Info "📋 显示实时日志..."
        & $composeCmd -f docker-compose.windows.yml logs -f
    } else {
        Write-Info "💡 查看实时日志请运行: .\start-windows.ps1 -Logs"
        Write-Info "💡 停止所有服务请运行: .\stop-windows.ps1"
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "脚本执行失败: $($_.Exception.Message)"
    exit 1
}
