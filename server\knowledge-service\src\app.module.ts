import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ElasticsearchModule } from '@nestjs/elasticsearch';

// 实体
import { KnowledgeBase } from './entities/knowledge-base.entity';
import { KnowledgeDocument } from './entities/knowledge-document.entity';
import { DocumentChunk } from './entities/document-chunk.entity';
import { DigitalHuman } from './entities/digital-human.entity';
import { DigitalHumanKnowledgeBinding } from './entities/digital-human-knowledge-binding.entity';

// 模块
import { DatabaseModule } from './database/database.module';
import { CacheModule } from './cache/cache.module';
import { StorageModule } from './storage/storage.module';
import { ProcessingModule } from './processing/processing.module';
import { UploadModule } from './upload/upload.module';
import { KnowledgeBasesModule } from './modules/knowledge-bases/knowledge-bases.module';
import { DocumentsModule } from './modules/documents/documents.module';
import { DigitalHumansModule } from './modules/digital-humans/digital-humans.module';
import { BindingsModule } from './modules/bindings/bindings.module';
import { AuthModule } from './modules/auth/auth.module';
import { HealthModule } from './modules/health/health.module';

// 控制器
import { HealthController } from './common/controllers/health.controller';

// 服务
import { LoggerService } from './common/services/logger.service';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 5432),
        username: configService.get('DB_USERNAME', 'postgres'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'knowledge_service'),
        entities: [
          KnowledgeBase,
          KnowledgeDocument,
          DocumentChunk,
          DigitalHuman,
          DigitalHumanKnowledgeBinding,
        ],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
      }),
      inject: [ConfigService],
    }),
    
    // Elasticsearch模块
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        node: configService.get('ELASTICSEARCH_URL', 'http://localhost:9200'),
        auth: {
          username: configService.get('ELASTICSEARCH_USERNAME', ''),
          password: configService.get('ELASTICSEARCH_PASSWORD', ''),
        },
      }),
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'knowledge-service-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // 微服务客户端
    ClientsModule.registerAsync([
      {
        name: 'SERVICE_REGISTRY',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get<string>('SERVICE_REGISTRY_HOST', 'localhost'),
            port: configService.get<number>('SERVICE_REGISTRY_PORT', 3010),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    
    // 核心模块
    DatabaseModule,
    CacheModule,
    StorageModule,
    ProcessingModule,
    UploadModule,
    
    // 业务模块
    KnowledgeBasesModule,
    DocumentsModule,
    DigitalHumansModule,
    BindingsModule,
    AuthModule,
    HealthModule,
  ],
  controllers: [HealthController],
  providers: [LoggerService],
  exports: [LoggerService],
})
export class AppModule {}
